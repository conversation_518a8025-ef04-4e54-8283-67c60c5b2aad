"use client";
import * as React from "react";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/index";
import { format } from "date-fns";

interface MonthPickerProps {
  value: string;
  onChange: (val: string) => void;
}

export default function MonthPicker({ value, onChange }: MonthPickerProps) {
  const [open, setOpen] = React.useState(false);
  const [selected, setSelected] = React.useState<Date | null>(
    value ? new Date(value) : null
  );
  const [year, setYear] = React.useState<number>(
    selected?.getFullYear() || new Date().getFullYear()
  );

  const months = [
    "Jan", "Feb", "Mar", "Apr",
    "May", "Jun", "Jul", "Aug",
    "Sep", "Oct", "Nov", "Dec",
  ];

  return (
    <PopoverPrimitive.Root open={open} onOpenChange={setOpen}>
      <PopoverPrimitive.Trigger asChild>
        <Button
          variant="outline"
          type="button"
          className="w-full h-8 text-sm justify-start text-left font-normal"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selected ? format(selected, "MMM yyyy") : "Select month"}
        </Button>
      </PopoverPrimitive.Trigger>

      {/* ✅ FIX: Popover aligned below trigger */}
      <PopoverPrimitive.Content
        side="bottom"
        align="start"
        sideOffset={6}
        className={cn(
          "z-[9999] w-[220px] rounded-md border border-gray-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 p-3 shadow-lg"
        )}
      >
        {/* ✅ Year navigation header */}
        <div className="flex items-center justify-between mb-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setYear((y) => y - 1)}
            className="h-6 w-6"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="font-medium text-sm">{year}</span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setYear((y) => y + 1)}
            className="h-6 w-6"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* ✅ Month grid */}
        <div className="grid grid-cols-4 gap-2">
          {months.map((m, i) => {
            const monthDate = new Date(year, i, 1);
            const isSelected =
              selected &&
              selected.getMonth() === i &&
              selected.getFullYear() === year;

            return (
              <Button
                key={m}
                size="sm"
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "h-8 text-xs",
                  isSelected
                    ? "bg-purple-600 text-white hover:bg-purple-700"
                    : "hover:bg-gray-100 dark:hover:bg-neutral-800"
                )}
                onClick={() => {
                  setSelected(monthDate);
                  onChange(format(monthDate, "yyyy-MM"));
                  setOpen(false);
                }}
              >
                {m}
              </Button>
            );
          })}
        </div>
      </PopoverPrimitive.Content>
    </PopoverPrimitive.Root>
  );
}
